package router

import (
	"api.appio.so/handlers"
	"api.appio.so/middlewares"
	"api.appio.so/models"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"github.com/appio-so/go-clientip"
	"github.com/appio-so/go-zaplog"
	"github.com/go-chi/chi/v5"
	chimid "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

type Config struct {
	Logger            *zap.Logger
	DB                *pgxpool.Pool
	DBFing            *pgxpool.Pool
	Config            *config.Config
	Services          *services.ServiceContainer
	RateLimitRequests int
	RateLimitWindow   int
	RateLimitBurst    int
}

func NewRouter(cfg Config) *chi.Mux {
	r := chi.NewRouter()
	r.Use(
		clientip.NewMiddleware().NewHandler,
		chimid.Recoverer,
		chimid.SetHeader("Content-Type", "application/json; charset=utf-8"),
		chimid.SetHeader("X-Content-Type-Options", "nosniff"),
		zaplog.NewMiddleware(cfg.Logger, zaplog.WithSentryScope(), zaplog.WithLogRequest(), zaplog.WithLogRequestBodyHeaders()).NewHandler,
		middlewares.TimeoutMiddleware(cfg.Config.Server.Timeout, cfg.Logger),
		middlewares.RateLimit(cfg.Logger, cfg.Config.Server.Env, cfg.RateLimitRequests, cfg.RateLimitWindow, cfg.RateLimitBurst),
	)

	// Public routes without auth
	r.Get("/", handlers.IndexHandler())
	r.Get("/robots.txt", handlers.RobotsTxtHandler())
	r.Get("/health-check", handlers.HealthCheckHandler())
	r.NotFound(handlers.NotFoundHandler())
	r.MethodNotAllowed(handlers.NotFoundHandler())

	featureFlagHandler := handlers.NewFeatureFlagHandler(cfg.Services.FeatureFlagService, cfg.Logger)
	serviceHandler := handlers.NewServiceHandler(cfg.Services.ServiceService, cfg.Logger)
	deviceHandler := handlers.NewDeviceHandler(cfg.Services.DeviceService, cfg.Logger)
	notificationHandler := handlers.NewNotificationHandler(cfg.Services.NotificationService, cfg.Logger)
	widgetHandler := handlers.NewWidgetHandler(cfg.Services.WidgetService, cfg.Services.WidgetConfigService, cfg.Logger)
	fingerprintHandler := handlers.NewFingerprintHandler(cfg.Services.FingerprintService, cfg.Logger)
	feedbackHandler := handlers.NewFeedbackHandler(cfg.Services.FeedbackService, cfg.Logger)

	r.Route("/", func(r chi.Router) {
		r.Use(
			cors.AllowAll().Handler, // TODO: allow only some domains + localhost
			middlewares.AuthRoleMiddleware(cfg.Services.APIKeyService, cfg.Services.JWTService),
		)

		// Auth: verifies role only
		r.With(middlewares.RoleMiddleware(cfg.Logger, roles.Api, roles.ApiDemo, roles.AppAppioSo, roles.DemoAppioSo, roles.IOS, roles.Android)).
			Get("/hi", handlers.HiHandler())

		r.Route("/mobile", func(r chi.Router) {
			r.Use(
				// Auth: verifies role only
				// NOTE: no service/device access validation. only restricted data manipulation allowed
				middlewares.RoleMiddleware(cfg.Logger, roles.IOS, roles.Android),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				middlewares.HeaderToContextMiddleware("X-Device-Id", middlewares.DvcIDKey{}, appioid.Parse, cfg.Logger),
				middlewares.HeaderToContextMiddleware("X-App-Platform", middlewares.PlatformKey{}, models.ParsePlatform, cfg.Logger),
				middlewares.LastSeenAtMiddleware(cfg.Services.LastSeenAtService, cfg.Logger),
			)

			r.Get("/ff", featureFlagHandler.Get)

			r.Post("/fingerprints/match", fingerprintHandler.Match)

			r.Get("/services", serviceHandler.ListByDeviceWithWidgetConfigs)
			r.Get("/services/{id}", serviceHandler.GetWithWidgetConfigs)

			r.Post("/devices", deviceHandler.CreateAndLink)
			r.Patch("/devices/{id}", deviceHandler.Update)
			r.Post("/devices/{id}/services", deviceHandler.LinkWithService)
			r.Delete("/devices/{id}", deviceHandler.Deactivate)

			r.Post("/feedback", feedbackHandler.Create)

			r.Get("/notifications", notificationHandler.ListDeliveredByDevice)

			r.Get("/widgets/{id}", widgetHandler.GetConfig)
		})

		r.Route("/demo-appio-so", func(r chi.Router) {
			r.Use(
				// Auth: verifies role and service ID prefix
				middlewares.RoleMiddleware(cfg.Logger, roles.DemoAppioSo),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
				middlewares.ValidateServiceIDPrefix("demo_svc", cfg.Logger),
			)

			r.Get("/services/{id}", serviceHandler.GetWithWidgets)
			r.Post("/services", serviceHandler.CreateDemo)
			r.Patch("/services/{id}", serviceHandler.Update)

			r.Get("/devices", deviceHandler.List)
			r.Post("/devices", deviceHandler.CreateAndLink)

			r.Get("/notifications", notificationHandler.List)
			r.Post("/notifications", notificationHandler.Create)

			r.Get("/widgets/{id}", widgetHandler.Get)
			r.Post("/widgets", widgetHandler.Create)
			r.Patch("/widgets/{id}", widgetHandler.Update)
		})

		r.Route("/app-appio-so", func(r chi.Router) {
			r.Use(
				// Auth: verifies role only
				middlewares.RoleMiddleware(cfg.Logger, roles.AppAppioSo),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),
			)

			r.Post("/fingerprints", fingerprintHandler.Create)
			r.Get("/services/{id}", serviceHandler.Get)
		})

		r.Route("/v1", func(r chi.Router) {
			r.Use(
				// Auth: verifies role + TODO: add validation role (user, org) + service (what if no service present?)
				middlewares.RoleMiddleware(cfg.Logger, roles.Api, roles.ApiDemo, roles.Dashboard),
				middlewares.HeaderToContextMiddleware("X-Service-Id", middlewares.SvcIDKey{}, appioid.Parse, cfg.Logger),

				// TODO: also to check that SvcIDKey belongs to this user,
				//       if nil set to something to 'lock' to prevents overwrite (in case HeaderToContextMiddleware is set after this middleware ?)
				middlewares.AuthMiddleware(cfg.Services.APIKeyService, cfg.Logger),
			)

			r.Get("/services", serviceHandler.ListByOrganization) // user/org

			r.Group(func(r chi.Router) {
				r.Use(middlewares.RequireContext(middlewares.SvcIDKey{}, cfg.Logger))

				r.Get("/services/{id}", serviceHandler.Get)
				r.Patch("/services/{id}", serviceHandler.Update)

				r.Get("/devices", deviceHandler.List)
				r.Get("/devices/{id}", deviceHandler.Get)
				r.Delete("/devices", deviceHandler.DeactivateByUser)
				r.Delete("/devices/{id}", deviceHandler.Deactivate)

				r.Get("/notifications", notificationHandler.List)
				r.Get("/notifications/{id}", notificationHandler.Get) // svcID - technically not needed?
				r.Post("/notifications", notificationHandler.Create)

				r.Get("/widgets", widgetHandler.List)
				r.Get("/widgets/{id}", widgetHandler.Get) // svcID - technically not needed?
				r.Post("/widgets", widgetHandler.Create)
				r.Patch("/widgets/{id}", widgetHandler.Update)  // svcID - technically not needed?
				r.Delete("/widgets/{id}", widgetHandler.Delete) // svcID - technically not needed?
			})
		})
	})

	return r
}
