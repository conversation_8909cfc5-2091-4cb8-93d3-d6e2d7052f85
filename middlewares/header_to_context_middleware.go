package middlewares

import (
	"context"
	"net/http"
	"reflect"

	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

// HeaderToContextMiddleware is a generic middleware that parses a header value using the provided parser
// and sets it in the request context. HTTP 400 is returned for invalid headers.
func HeaderToContextMiddleware[T any](
	headerName string,
	contextKey any,
	parser func(string) (T, error),
	logger *zap.Logger,
) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			headerValue := r.Header.Get(headerName)

			if headerValue != "" {
				parsed, err := parser(headerValue)
				if err != nil {
					logger.Error("invalid header value", zap.String("header", headerName), zap.String("value", headerValue), zap.Error(err))
					http.Error(w, "Invalid "+headerName+" header", http.StatusBadRequest)
					return
				}

				// if already set and the new value is different
				current, ok := ctx.Value(contextKey).(T)
				if ok && !valuesEqual(current, parsed) {
					logger.Error("Context value already set", zap.Any("current", current), zap.Any("new", parsed))
					http.Error(w, "Invalid "+headerName+" header overwrite", http.StatusBadRequest)
					return
				}

				ctx = context.WithValue(ctx, contextKey, parsed)
			}

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// valuesEqual compares two values efficiently based on their type
// This avoids reflection for common types: string, int, models.Platform, and appioid.ID
func valuesEqual[T any](a, b T) bool {
	// Use type switch for performance with known types
	switch va := any(a).(type) {
	case string:
		vb := any(b).(string)
		return va == vb
	case int:
		vb := any(b).(int)
		return va == vb
	case *appioid.ID:
		vb := any(b).(*appioid.ID)
		if va == nil && vb == nil {
			return true
		}
		if va == nil || vb == nil {
			return false
		}
		return va.String() == vb.String()
	case appioid.ID:
		vb := any(b).(appioid.ID)
		return va.String() == vb.String()
	default:
		// Fallback to reflection for other types
		return reflect.DeepEqual(a, b)
	}
}
