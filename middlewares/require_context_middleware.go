package middlewares

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"go.uber.org/zap"
)

// RequireContext ensures that a context value by the given key is set and not nil.
// If the value is not set or is nil, the middleware returns HTTP 400 Bad Request.
func RequireContext(contextKey any, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			value := ctx.Value(contextKey)

			if value == nil {
				logger.Error("required context value is missing or nil",
					zap.String("context_key", getContextKeyName(contextKey)),
					zap.String("method", r.Method),
					zap.String("path", r.URL.Path))
				helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
				return
			}

			next.Serve<PERSON><PERSON>(w, r)
		})
	}
}

// getContextKeyName returns a string representation of the context key for logging
func getContextKeyName(key any) string {
	switch key.(type) {
	case SvcIDKey:
		return "SvcIDKey"
	case DvcIDKey:
		return "DvcIDKey"
	case OrgIDKey:
		return "OrgIDKey"
	case PlatformKey:
		return "PlatformKey"
	default:
		return "unknown"
	}
}
