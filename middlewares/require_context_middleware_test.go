package middlewares

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestRequireContext(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that should only be called if middleware passes
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - SvcIDKey present and not nil", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		middleware := RequireContext(SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - DvcIDKey present and not nil", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), DvcIDKey{}, dvcID)

		middleware := RequireContext(DvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - OrgIDKey present and not nil", func(t *testing.T) {
		orgID := appioid.MustParse("org_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), OrgIDKey{}, orgID)

		middleware := RequireContext(OrgIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - SvcIDKey not present in context", func(t *testing.T) {
		ctx := context.Background() // No SvcIDKey in context

		middleware := RequireContext(SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - SvcIDKey is nil", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), SvcIDKey{}, nil)

		middleware := RequireContext(SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - DvcIDKey not present in context", func(t *testing.T) {
		ctx := context.Background() // No DvcIDKey in context

		middleware := RequireContext(DvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - DvcIDKey is nil", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), DvcIDKey{}, nil)

		middleware := RequireContext(DvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Success - Unknown context key type", func(t *testing.T) {
		type CustomKey struct{}
		customValue := "some-value"
		ctx := context.WithValue(context.Background(), CustomKey{}, customValue)

		middleware := RequireContext(CustomKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - Unknown context key type is nil", func(t *testing.T) {
		type CustomKey struct{}
		ctx := context.WithValue(context.Background(), CustomKey{}, nil)

		middleware := RequireContext(CustomKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Multiple middleware chain", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, DvcIDKey{}, dvcID)

		// Chain multiple RequireContext middlewares
		middleware1 := RequireContext(SvcIDKey{}, logger)
		middleware2 := RequireContext(DvcIDKey{}, logger)
		handler := middleware1(middleware2(mockHandler))

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Multiple middleware chain - first fails", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), DvcIDKey{}, dvcID)
		// Missing SvcIDKey

		// Chain multiple RequireContext middlewares
		middleware1 := RequireContext(SvcIDKey{}, logger)
		middleware2 := RequireContext(DvcIDKey{}, logger)
		handler := middleware1(middleware2(mockHandler))

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Multiple middleware chain - second fails", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)
		// Missing DvcIDKey

		// Chain multiple RequireContext middlewares
		middleware1 := RequireContext(SvcIDKey{}, logger)
		middleware2 := RequireContext(DvcIDKey{}, logger)
		handler := middleware1(middleware2(mockHandler))

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Integration - matches existing router usage pattern", func(t *testing.T) {
		// This test simulates the actual usage in router.go line 146:
		// r.Use(middlewares.RequireContext(middlewares.SvcIDKey{}, cfg.Logger))

		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		middleware := RequireContext(SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/v1/services/svc_00000000000000000000000001", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Integration - missing context fails like expected", func(t *testing.T) {
		// This test simulates what happens when SvcIDKey is missing
		ctx := context.Background() // No SvcIDKey

		middleware := RequireContext(SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/v1/services/svc_00000000000000000000000001", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")

		// Verify the error structure matches what helpers.RenderJSONError produces
		errorObj, ok := response["error"].(map[string]interface{})
		assert.True(t, ok)
		assert.Contains(t, errorObj, "message")
		assert.Equal(t, "Invalid input data", errorObj["message"])
	})
}

func TestGetContextKeyName(t *testing.T) {
	t.Run("Known context keys", func(t *testing.T) {
		assert.Equal(t, "SvcIDKey", getContextKeyName(SvcIDKey{}))
		assert.Equal(t, "DvcIDKey", getContextKeyName(DvcIDKey{}))
		assert.Equal(t, "OrgIDKey", getContextKeyName(OrgIDKey{}))
		assert.Equal(t, "PlatformKey", getContextKeyName(PlatformKey{}))
	})

	t.Run("Unknown context key", func(t *testing.T) {
		type CustomKey struct{}
		assert.Equal(t, "unknown", getContextKeyName(CustomKey{}))
		assert.Equal(t, "unknown", getContextKeyName("string-key"))
		assert.Equal(t, "unknown", getContextKeyName(123))
	})
}
