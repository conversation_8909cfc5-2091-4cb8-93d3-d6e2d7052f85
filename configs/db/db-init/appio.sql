/*
    Using plural for table names to match API endpoints.
    ID prefixes:
        ak_         api keys
        dvc_        device
        dvcsvc_     device connected to service
        fdb_        feedback
        ff_         feature flag
        ntf_        notification
        ntfdlv_     notification delivery
        org_        organization
        svc_        service
        usr_        user
        wgt_        widget
 */
SET TIME ZONE 'UTC';

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='appio_id') THEN
        CREATE DOMAIN appio_id AS TEXT CHECK (VALUE ~ '^[a-z_]{2,10}_[0-9a-hjkmnp-tv-z]{26}$');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='non_empty_text') THEN
        CREATE DOMAIN non_empty_text AS TEXT NOT NULL CHECK (VALUE <> '');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='platform') THEN
        CREATE TYPE platform AS ENUM (
            'android',
            'ios'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='queue_status') THEN
        CREATE TYPE queue_status AS ENUM (
            'created',
            'queued',
            'processing',
            'completed',
            'failed',
            'retry',
            'paused',
            'cancelled',
            'skipped',
            'delayed',
            'expired'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='notification_type') THEN
        CREATE TYPE notification_type AS ENUM (
            'background',
            'foreground'
        );
    END IF;
END $$;

SET ROLE appio_api; -- to make this explicit

CREATE TABLE organizations
(
    id             appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with org_
    name           non_empty_text,
    created_at     TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deactivated_at TIMESTAMPTZ,

    CONSTRAINT chk_organizations_deactivated_at CHECK (deactivated_at IS NULL OR deactivated_at > created_at)
);

CREATE TABLE users (
    id                  appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with usr_
    organization_id     appio_id NOT NULL,
    external_id         non_empty_text UNIQUE,
    email               non_empty_text UNIQUE,
    name                non_empty_text,
    password_hash       non_empty_text,
    created_at          TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deactivated_at      TIMESTAMPTZ,

    CONSTRAINT fk_users_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
    CONSTRAINT chk_users_deactivated_at CHECK (deactivated_at IS NULL OR deactivated_at > created_at)
);

CREATE TABLE IF NOT EXISTS services
(
    id                  appio_id NOT NULL PRIMARY KEY,       -- ULID prefixed with svc_
    organization_id     appio_id NOT NULL,                                                     -- TODO: in separate pairing table?
    created_at          TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deactivated_at      TIMESTAMPTZ,
    title               non_empty_text,
    description         TEXT NOT NULL DEFAULT '',
    logo_url            non_empty_text,
    banner_url          TEXT NOT NULL DEFAULT '',
    url                 TEXT NOT NULL DEFAULT '',
    text_color          TEXT NOT NULL DEFAULT '',
    background_color    TEXT NOT NULL DEFAULT '',
    accent_color        TEXT NOT NULL DEFAULT '',

    CONSTRAINT chk_services_deactivated_at CHECK (deactivated_at IS NULL OR deactivated_at > created_at),
    CONSTRAINT fk_services_organization FOREIGN KEY (organization_id) REFERENCES organizations(id)
);
COMMENT ON COLUMN services.deactivated_at IS 'if NULL then active';
CREATE INDEX idx_active_services_org ON services (organization_id) WHERE deactivated_at IS NULL;

CREATE TABLE IF NOT EXISTS devices
(
    id                          appio_id NOT NULL PRIMARY KEY,   -- ULID prefixed with dvc_
    created_at                  TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    name                        non_empty_text,
    platform                    platform NOT NULL,
    os_version                  non_empty_text,
    model                       non_empty_text,
    device_token                TEXT NOT NULL DEFAULT '',       -- Apple APN or Android token for push notifications
    device_identifier           TEXT NOT NULL DEFAULT '',
    notifications_enabled       BOOLEAN NOT NULL DEFAULT FALSE, -- on device per app
    marketing_name              TEXT NOT NULL DEFAULT '',
    last_seen_at                TIMESTAMPTZ                     -- last time this device called the API
);
COMMENT ON COLUMN devices.device_token IS 'for push notifications';

/* once a device can be subscribed to multiple services and for each have different customer_user_id */
CREATE TABLE IF NOT EXISTS dvc_svc
(
    id                      appio_id NOT NULL PRIMARY KEY,   -- ULID prefixed with dvcsvc_
    service_id              appio_id NOT NULL,
    device_id               appio_id NOT NULL,
    customer_user_id        non_empty_text,
    created_at              TIMESTAMPTZ NOT NULL DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deactivated_at          TIMESTAMPTZ,
    notifications_enabled   BOOLEAN NOT NULL DEFAULT TRUE  -- by user per service

    CONSTRAINT chk_dvc_svc_deactivated_at CHECK (deactivated_at IS NULL OR deactivated_at > created_at),
    CONSTRAINT fk_dvc_svc_service FOREIGN KEY (service_id) REFERENCES services(id),
    CONSTRAINT fk_dvc_svc_device FOREIGN KEY (device_id) REFERENCES devices(id)
);
COMMENT ON COLUMN dvc_svc.deactivated_at IS 'if NULL then active';
CREATE INDEX idx_dvc_svc_service_id ON dvc_svc (service_id);    -- TODO: should add `WHERE deactivated_at IS NULL;` ?
CREATE INDEX idx_dvc_svc_device_id ON dvc_svc (device_id);      -- TODO: should add `WHERE deactivated_at IS NULL;` ?
CREATE UNIQUE INDEX uniq_active_dvc_svc ON dvc_svc (device_id, service_id) WHERE deactivated_at IS NULL;

CREATE TABLE IF NOT EXISTS notifications
(
    id              appio_id NOT NULL PRIMARY KEY,     -- ULID prefixed with ntf_
    service_id      appio_id NOT NULL,
    created_at      TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    scheduled_at    TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    type            notification_type NOT NULL,
    payload         JSONB NOT NULL,
    status          queue_status NOT NULL,
-- channel_id, queued_at (to track time between creation and queueing=creating notification_deliveries entries per device), expires_at (unless per device), sent_at (unless per device)

    CONSTRAINT fk_notifications_service FOREIGN KEY (service_id) REFERENCES services(id)
);
CREATE INDEX idx_notifications_type_svc_id_status_scheduled_at_created_at ON notifications (type, service_id, status, scheduled_at ASC, created_at ASC);
CREATE INDEX idx_notifications_status_created_at ON notifications (status, created_at ASC);
CREATE INDEX idx_notifications_id_type_service_id ON notifications (id, type, service_id);

CREATE TABLE IF NOT EXISTS notification_deliveries
(
    id               appio_id NOT NULL PRIMARY KEY,      -- ULID prefixed with ntfdlv_
    notification_id  appio_id NOT NULL,
    device_id        appio_id NOT NULL,
    created_at       TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    updated_at       TIMESTAMPTZ,               -- for status updates
    scheduled_at     TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    status           queue_status NOT NULL,
    error_message    TEXT NOT NULL DEFAULT '',
-- scheduled_at (for timing deliveries), read_at, expire_at, error_message

    CONSTRAINT unique_notification_deliveries_notification_device UNIQUE (notification_id, device_id),
    CONSTRAINT fk_notification_deliveries_notification FOREIGN KEY (notification_id) REFERENCES notifications(id),
    CONSTRAINT fk_notification_deliveries_device FOREIGN KEY (device_id) REFERENCES devices(id)
);
CREATE INDEX idx_notification_notification_id_id ON notification_deliveries (notification_id, id);
CREATE INDEX idx_notification_device_id_notification_id ON notification_deliveries (device_id, notification_id);
CREATE INDEX idx_notification_deliveries_notification_id_status ON notification_deliveries (notification_id, status);
CREATE INDEX idx_notification_deliveries_status_created_at ON notification_deliveries (status, created_at);
CREATE INDEX idx_notification_device_id_updated_at ON notification_deliveries (device_id, updated_at DESC);
COMMENT ON COLUMN notification_deliveries.updated_at IS 'track status update';

CREATE TABLE IF NOT EXISTS api_keys
(
    id              appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with ak_
    api_key         VARCHAR(60) NOT NULL UNIQUE,    -- [a-z_]{0,10}[a-zA-Z0-9]{50} prefixed with: dev_,  demo_dev_,  prod_,  demo_prod_
    organization_id appio_id,                       -- TODO: change to NOT NULL once completed on server
    service_id      appio_id,                       -- can be NULL, optional restriction to a single service   TODO: should be separate table?
    created_at      TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deactivated_at  TIMESTAMPTZ
);
COMMENT ON COLUMN api_keys.deactivated_at IS 'if NULL then active';
CREATE INDEX idx_active_api_keys_org_service ON api_keys (organization_id, service_id) WHERE deactivated_at IS NULL;

CREATE TABLE IF NOT EXISTS widgets
(
    id              appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with wgt_
    service_id      appio_id NOT NULL,
    template        non_empty_text,
    source          JSONB NOT NULL,
    created_at      TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    deleted_at      TIMESTAMPTZ,

    CONSTRAINT fk_widgets_service FOREIGN KEY (service_id) REFERENCES services(id)
);
CREATE INDEX idx_widgets_service_id ON widgets (service_id);    -- TODO: should add `WHERE deleted_at IS NULL;` ?

CREATE TABLE IF NOT EXISTS feature_flags
(
    id              appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with ff_
    platform        platform NOT NULL,
    version         non_empty_text,                 -- app version
    config          non_empty_text UNIQUE,

    CONSTRAINT unique_feature_flags_version_platform UNIQUE (version, platform)
);
CREATE INDEX idx_feature_flags_version_id ON feature_flags (version);

CREATE TABLE IF NOT EXISTS device_names
(
    brand       non_empty_text, -- lowercase. for iOS, we use apple
    model       non_empty_text, -- lowercase. for iOS, this is an identifier
    name        non_empty_text,

    CONSTRAINT unique_device_names_brand_model UNIQUE (brand, model)
);
CREATE INDEX idx_device_names_brand_model ON device_names (brand, model);

CREATE TABLE IF NOT EXISTS feedbacks
(
    id              appio_id NOT NULL PRIMARY KEY,  -- ULID prefixed with fdb_
    platform        platform NOT NULL,
    version         non_empty_text,                 -- app version
    ip              inet NOT NULL,
    service_id      appio_id,                       -- can be null from intro screen
    device_id       appio_id,                       -- can be null from intro screen
    created_at      TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    message         non_empty_text,

    CONSTRAINT fk_feedbacks_service FOREIGN KEY (service_id) REFERENCES services(id),
    CONSTRAINT fk_feedbacks_device FOREIGN KEY (device_id) REFERENCES devices(id)
);

----------- DOCS.APPIO.SO ------------ DOCS.APPIO.SO ------------ DOCS.APPIO.SO ------------ DOCS.APPIO.SO -------------

INSERT INTO device_names (brand, model, name) VALUES
    (
        'apple',
        'i386',
        'Simulator'
    ),
    (
        'apple',
        'x86_64',
        'Simulator'
    ),
    (
        'apple',
        'arm64',
        'Simulator'
    ),
    (
        'google',
        'sdk_gphone_arm64',
        'Simulator'
    ),
    (
        'google',
        'sdk_gphone_x86_64',
        'Simulator'
    ),
    (
        'google',
        'sdk_gphone64_x86_64',
        'Simulator'
    ),
    (
        'google',
        'sdk_gphone64_arm64',
        'Simulator'
    ),
    (
        'google',
        'sdk_gphone16k_arm64',
        'Simulator'
    );

INSERT INTO organizations (id, name) VALUES
    (
        'org_00000000000000000000000000',
        'Appio'
    );

INSERT INTO users (id, organization_id, external_id, email, name, password_hash) VALUES
    (
        'usr_00000000000000000000000000',
        'org_00000000000000000000000000',
        'external',
        '<EMAIL>',
        'Appio User',
        '***'
    );

INSERT INTO services (id, organization_id, title, description, banner_url, logo_url, url, text_color, background_color, accent_color) VALUES
    (
        'svc_00dddddd000000ccccccssssss',
        'org_00000000000000000000000000',
        'Appio Docs',
        'Appio in action — download the app and experience its full potential.',
        'https://cdn.appio.so/app/docs.appio.so/banner.jpg',
        'https://cdn.appio.so/app/docs.appio.so/logo.png',
        'https://docs.appio.so',
        '#ff0000',
        '#ff00ff',
        '#00ffff'
    );

INSERT INTO api_keys (id, api_key, organization_id, service_id) VALUES
    (
        'ak_01jq45z8cakcm5p2q305tw8dw9',
        'docs_g3psUMsuKZ7NGGJvuk1csf47pvJfukz97cS5ZrOuHnY98yhY5A',
        'org_00000000000000000000000000',
        'svc_00dddddd000000ccccccssssss'
    );

INSERT INTO devices (id, name, platform, os_version, model, device_token, notifications_enabled, device_identifier, marketing_name) VALUES
    (
        'dvc_01jmpmh9fvxgyym44sqanjr9hs',
        'iPhone 13',
        'ios',
        '18.3',
        'iPhone',
        '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        true,
        'iPhone14,5',
        'iPhone 13'
    );

INSERT INTO dvc_svc (id, service_id, device_id, customer_user_id) VALUES
    (
        'dvcsvc_01jq47ppcr6se7709t7b3h4q9r',
        'svc_00dddddd000000ccccccssssss',
        'dvc_01jmpmh9fvxgyym44sqanjr9hs',
        '23d0e9848fe0ad06272dea39a03679ff'
    );

INSERT INTO notifications (id, service_id, type, payload, status) VALUES
    (
        'ntf_01jmpmgb6my0s57c960q1s862v',
        'svc_00dddddd000000ccccccssssss',
        'foreground',
        '{"title": "Notification","message": "Hello from Appio Docs","link": "https://docs.appio.so","image_url": "https://cdn.appio.so/app/docs.appio.so/banner.jpg"}',
        'completed'
    );

INSERT INTO notification_deliveries (id, notification_id, updated_at, device_id, status) VALUES
    (
        'ntfdlv_01jq48jnysm3ctpp64jv5fhhed',
        'ntf_01jmpmgb6my0s57c960q1s862v',
        CURRENT_TIMESTAMP,
        'dvc_01jmpmh9fvxgyym44sqanjr9hs',
        'completed'
    );

INSERT INTO widgets (id, service_id, template, source) VALUES
    (
        'wgt_01jmpr3vt1008txv1jehgdeny7',
        'svc_00dddddd000000ccccccssssss',
        'number',
        '{"type": "static", "data": 123}'
    );


--------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------


INSERT INTO feature_flags (id, platform, version, config) VALUES
    (
        'ff_00000000000000000000000000',
        'ios',
        '1.1',
        '{"intro":"A"}'
    );

INSERT INTO feature_flags (id, platform, version, config) VALUES
    (
        'ff_11111111111111111111111111',
        'android',
        '1.0',
        '{"intro":"B"}'
    );

INSERT INTO api_keys (id, api_key, organization_id) VALUES
    (
        'ak_22222222222222222222222222',
        'dev_99999999999999999999999999999999999999999999999999', -- example of local customer api user
        'org_00000000000000000000000000'
    );

-- # Create demo data. Needed for docs.appio.so to work
INSERT INTO services (id, organization_id, title, description, banner_url, logo_url, url, text_color, background_color, accent_color) VALUES
    (
        'svc_00000000000000000000000000',
        'org_00000000000000000000000000',
        'Appio',
        'Appio in action — download the app and experience its full potential.',
        'https://cdn.appio.so/app/appio.so/banner.jpg',
        'https://cdn.appio.so/app/appio.so/logo.png',
        'https://appio.so',
        '#333333',
        '#f0f0f0',
        '#00aaff'
    );

INSERT INTO services (id, organization_id, title, description, banner_url, logo_url, url, text_color, background_color, accent_color) VALUES
   (
        'svc_11111111111111111111111111',
        'org_00000000000000000000000000',
        'Appio for June.so',
        'Appio for June is product analytics software for B2B SaaS.',
        'https://cdn.appio.so/app/june.so/banner.png',
        'https://cdn.appio.so/app/june.so/logo.svg',
        'https://june.so',
        '#6868f7',
        '#fafafa',
        '#ff000'
   );

INSERT INTO services (id, organization_id, title, description, banner_url, logo_url, url, text_color, background_color, accent_color) VALUES
    (
        'demo_svc_00000000000000000000000000',
        'org_00000000000000000000000000',
        'Appio Demo',
        'Appio in action — download the app and experience its full potential.',
        'https://cdn.appio.so/app/appio.so/banner.jpg',
        'https://cdn.appio.so/app/appio.so/logo.png',
        'https://demo.appio.so',
        '#00aaff',
        '#cccccc',
        '#ff0000'
    );



INSERT INTO devices (id, name, platform, os_version, model, device_token, notifications_enabled, device_identifier, marketing_name) VALUES
    (
        'dvc_00000000000000000000000000',
        'My Personal Phone',
        'android',
        '14 (API 34)',
        'samsung┼SM-A136B',
        '*** token ***',
        true,
        '6874387efd0adcda',
        'Samsung Galaxy A13'
    );

INSERT INTO dvc_svc (id, service_id, device_id, customer_user_id) VALUES
    (
        'dvcsvc_00000000000000000000000000',
        'svc_00000000000000000000000000',
        'dvc_00000000000000000000000000',
        'ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a'
    );

INSERT INTO devices (id, name, platform, os_version, model, device_token, notifications_enabled, device_identifier, marketing_name) VALUES
    (
        'dvc_11111111111111111111111111',
        'iPad Pro 13-inch',
        'ios',
        '17.1',
        'iPad',
        '',
        false,
        'iPad7,6',
        'iPad Pro 13-inch'
    );

INSERT INTO dvc_svc (id, service_id, device_id, customer_user_id) VALUES
    (
        'dvcsvc_11111111111111111111111111',
        'svc_00000000000000000000000000',
        'dvc_11111111111111111111111111',
        'ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a'
    );

INSERT INTO dvc_svc (id, service_id, device_id, customer_user_id) VALUES
    (
        'dvcsvc_22222222222222222222222222',
        'demo_svc_00000000000000000000000000',
        'dvc_11111111111111111111111111',
        'another-customer-user-id'
    );



INSERT INTO widgets (id, service_id, template, source) VALUES
    (
        'wgt_00000000000000000000000000',
        'svc_00000000000000000000000000',
        'ring',
        '{"type": "static", "data": 10}'
    );



INSERT INTO notifications (id, service_id, type, payload, status) VALUES
    (
        'ntf_00000000000000000000000000',
        'svc_00000000000000000000000000',
        'foreground',
        '{"message": "DB init"}',
        'completed'
    );

INSERT INTO notification_deliveries (id, notification_id, updated_at, device_id, status) VALUES
    (
        'ntfdlv_00000000000000000000000000',
        'ntf_00000000000000000000000000',
        CURRENT_TIMESTAMP,
        'dvc_00000000000000000000000000',
        'created'
    );

INSERT INTO notifications (id, service_id, type, payload, status) VALUES
    (
        'ntf_11111111111111111111111111',
        'demo_svc_00000000000000000000000000',
        'foreground',
        '{"message": "DB init"}',
        'completed'
    );

INSERT INTO notification_deliveries (id, notification_id, updated_at, device_id, status) VALUES
    (
        'ntfdlv_11111111111111111111111111',
        'ntf_11111111111111111111111111',
        CURRENT_TIMESTAMP,
        'dvc_11111111111111111111111111',
        'completed'
    );

INSERT INTO notifications (id, service_id, type, payload, status) VALUES
    (
        'ntf_22222222222222222222222222',
        'svc_00000000000000000000000000',
        'foreground',
        '{"message": "DB init"}',
        'queued'
    );

INSERT INTO notification_deliveries (id, notification_id, updated_at, device_id, status) VALUES
    (
        'ntfdlv_22222222222222222222222222',
        'ntf_22222222222222222222222222',
        CURRENT_TIMESTAMP,
        'dvc_00000000000000000000000000',
        'queued'
    );